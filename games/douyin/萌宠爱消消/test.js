/**
 * 萌宠爱消消 - 测试文件
 * 用于验证游戏功能是否正常
 */

// 模拟抖音小游戏环境
global.tt = {
    createCanvas: () => ({
        width: 375,
        height: 667,
        getContext: () => ({
            clearRect: () => {},
            fillRect: () => {},
            fillText: () => {},
            strokeRect: () => {},
            beginPath: () => {},
            arc: () => {},
            fill: () => {},
            stroke: () => {},
            roundRect: () => {},
            createLinearGradient: () => ({
                addColorStop: () => {}
            })
        })
    }),
    getSystemInfoSync: () => ({
        windowWidth: 375,
        windowHeight: 667
    }),
    onTouchStart: () => {},
    onTouchMove: () => {},
    onTouchEnd: () => {},
    offTouchStart: () => {},
    offTouchMove: () => {},
    offTouchEnd: () => {},
    getStorageSync: () => null,
    setStorageSync: () => {},
    createInnerAudioContext: () => ({
        src: '',
        loop: false,
        volume: 1,
        play: () => {},
        stop: () => {},
        pause: () => {},
        destroy: () => {}
    })
};

// 测试配置文件
try {
    const CONFIG = require('./config.js');
    console.log('✅ 配置文件加载成功');
    console.log('游戏名称:', CONFIG.game.name);
    console.log('关卡数量:', CONFIG.levels.length);
} catch (error) {
    console.error('❌ 配置文件加载失败:', error.message);
}

// 测试管理器
try {
    const AudioManager = require('./manager/audioManager.js');
    const audioManager = new AudioManager();
    console.log('✅ 音频管理器创建成功');
} catch (error) {
    console.error('❌ 音频管理器创建失败:', error.message);
}

try {
    const DataManager = require('./manager/dataManager.js');
    const dataManager = new DataManager();
    console.log('✅ 数据管理器创建成功');
    console.log('当前关卡:', dataManager.getCurrentLevel());
    console.log('最高分:', dataManager.getHighScore());
} catch (error) {
    console.error('❌ 数据管理器创建失败:', error.message);
}

try {
    const ThemeManager = require('./manager/themeManager.js');
    const themeManager = new ThemeManager();
    console.log('✅ 主题管理器创建成功');
    console.log('默认主题:', themeManager.getDefaultTheme());
} catch (error) {
    console.error('❌ 主题管理器创建失败:', error.message);
}

// 测试页面
try {
    const GamePage = require('./pages/GamePage.js');
    console.log('✅ 游戏页面模块加载成功');
} catch (error) {
    console.error('❌ 游戏页面模块加载失败:', error.message);
}

try {
    const RankPage = require('./pages/RankPage.js');
    console.log('✅ 排行榜页面模块加载成功');
} catch (error) {
    console.error('❌ 排行榜页面模块加载失败:', error.message);
}

try {
    const SettingPage = require('./pages/SettingPage.js');
    console.log('✅ 设置页面模块加载成功');
} catch (error) {
    console.error('❌ 设置页面模块加载失败:', error.message);
}

// 测试主游戏
try {
    const Game = require('./game.js');
    console.log('✅ 主游戏模块加载成功');
    console.log('🎮 萌宠爱消消游戏初始化完成！');
} catch (error) {
    console.error('❌ 主游戏模块加载失败:', error.message);
}

console.log('\n🎯 测试完成！如果看到所有 ✅ 标记，说明游戏可以正常运行。');
