/**
 * 萌宠爱消消 - 游戏页面
 * 游戏主界面和逻辑实现
 */

const CONFIG = require('../config.js');

class GamePage {
    constructor(app) {
        this.app = app;
        this.canvas = app.canvas;
        this.ctx = app.ctx;
        
        // 游戏状态
        this.gameState = {
            board: [],
            selectedTile: null,
            isAnimating: false,
            score: 0,
            combo: 0,
            level: app.globalData.currentLevel || 1,
            targetScore: CONFIG.levels[0].targetScore,
            props: {
                refresh: CONFIG.props.refresh.maxCount,
                bomb: CONFIG.props.bomb.maxCount,
                clear: CONFIG.props.clear.maxCount
            }
        };

        this.init();
    }

    /**
     * 初始化游戏页面
     */
    init() {
        this.initBoard();
        this.draw();
        this.bindEvents();
    }

    /**
     * 初始化游戏棋盘
     */
    initBoard() {
        const levelConfig = CONFIG.levels[this.gameState.level - 1];
        const animalCount = levelConfig.animalTypes;
        
        // 创建空棋盘
        this.gameState.board = [];
        for (let row = 0; row < CONFIG.board.rows; row++) {
            this.gameState.board[row] = [];
            for (let col = 0; col < CONFIG.board.cols; col++) {
                // 随机生成萌宠类型，但避免初始就有3消
                let animalType;
                do {
                    animalType = Math.floor(Math.random() * animalCount);
                } while (this.wouldCreateMatch(row, col, animalType));
                
                this.gameState.board[row][col] = {
                    type: animalType,
                    x: CONFIG.board.startX + col * (CONFIG.board.tileSize + CONFIG.board.padding),
                    y: CONFIG.board.startY + row * (CONFIG.board.tileSize + CONFIG.board.padding),
                    row: row,
                    col: col,
                    selected: false,
                    effect: null // 'rocket' 或 'bomb'
                };
            }
        }

        // 检查是否有可行移动
        if (!this.hasValidMoves()) {
            this.initBoard(); // 重新生成
        }
    }

    /**
     * 检查是否会创建消除
     */
    wouldCreateMatch(row, col, type) {
        // 检查横向
        let hCount = 1;
        for (let i = col - 1; i >= 0 && this.gameState.board[row] && this.gameState.board[row][i] && this.gameState.board[row][i].type === type; i--) {
            hCount++;
        }
        for (let i = col + 1; i < CONFIG.board.cols && this.gameState.board[row] && this.gameState.board[row][i] && this.gameState.board[row][i].type === type; i++) {
            hCount++;
        }
        if (hCount >= 3) return true;

        // 检查纵向
        let vCount = 1;
        for (let i = row - 1; i >= 0 && this.gameState.board[i] && this.gameState.board[i][col] && this.gameState.board[i][col].type === type; i--) {
            vCount++;
        }
        for (let i = row + 1; i < CONFIG.board.rows && this.gameState.board[i] && this.gameState.board[i][col] && this.gameState.board[i][col].type === type; i++) {
            vCount++;
        }
        if (vCount >= 3) return true;

        return false;
    }

    /**
     * 交换指定位置的方块
     */
    swapTilesByPosition(row1, col1, row2, col2) {
        const tile1 = this.gameState.board[row1][col1];
        const tile2 = this.gameState.board[row2][col2];
        
        if (tile1 && tile2) {
            const tempType = tile1.type;
            tile1.type = tile2.type;
            tile2.type = tempType;
            
            const tempEffect = tile1.effect;
            tile1.effect = tile2.effect;
            tile2.effect = tempEffect;
        }
    }

    /**
     * 检查是否有可行移动
     */
    hasValidMoves() {
        for (let row = 0; row < CONFIG.board.rows; row++) {
            for (let col = 0; col < CONFIG.board.cols; col++) {
                // 检查与右边交换
                if (col < CONFIG.board.cols - 1) {
                    this.swapTilesByPosition(row, col, row, col + 1);
                    if (this.findMatches().length > 0) {
                        this.swapTilesByPosition(row, col, row, col + 1); // 交换回来
                        return true;
                    }
                    this.swapTilesByPosition(row, col, row, col + 1); // 交换回来
                }

                // 检查与下边交换
                if (row < CONFIG.board.rows - 1) {
                    this.swapTilesByPosition(row, col, row + 1, col);
                    if (this.findMatches().length > 0) {
                        this.swapTilesByPosition(row, col, row + 1, col); // 交换回来
                        return true;
                    }
                    this.swapTilesByPosition(row, col, row + 1, col); // 交换回来
                }
            }
        }
        return false;
    }

    /**
     * 绘制游戏界面
     */
    draw() {
        this.clearCanvas();
        this.drawBackground();
        this.drawUI();
        this.drawBoard();
        this.drawProps();
    }

    /**
     * 清空画布
     */
    clearCanvas() {
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
    }

    /**
     * 绘制背景
     */
    drawBackground() {
        const themeManager = new (require('../manager/themeManager.js'))();
        const theme = themeManager.getTheme(this.app.globalData.theme);
        this.ctx.fillStyle = theme.backgroundColor;
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
    }

    /**
     * 绘制UI信息
     */
    drawUI() {
        const ctx = this.ctx;
        const themeManager = new (require('../manager/themeManager.js'))();
        const theme = themeManager.getTheme(this.app.globalData.theme);
        const layout = CONFIG.layout.game;

        // 绘制分数
        ctx.fillStyle = theme.textColor;
        ctx.font = 'bold 30px Arial';
        ctx.textAlign = 'left';
        ctx.fillText(`分数: ${this.gameState.score}`, layout.score.x, layout.score.y);

        // 绘制目标分数
        ctx.textAlign = 'right';
        ctx.fillText(`目标: ${this.gameState.targetScore}`, this.canvas.width - layout.target.x, layout.target.y);

        // 绘制关卡信息
        ctx.textAlign = 'center';
        ctx.font = 'bold 24px Arial';
        ctx.fillText(`第 ${this.gameState.level} 关`, this.canvas.width / 2, 50);

        // 绘制连击信息
        if (this.gameState.combo > 1) {
            ctx.fillStyle = '#FF5722';
            ctx.font = 'bold 36px Arial';
            ctx.fillText(`连击 x${this.gameState.combo}`, this.canvas.width / 2, 100);
        }
    }

    /**
     * 绘制棋盘
     */
    drawBoard() {
        const ctx = this.ctx;
        
        for (let row = 0; row < CONFIG.board.rows; row++) {
            for (let col = 0; col < CONFIG.board.cols; col++) {
                const tile = this.gameState.board[row][col];
                if (tile) {
                    this.drawTile(tile);
                }
            }
        }
    }

    /**
     * 绘制单个方块
     */
    drawTile(tile) {
        const ctx = this.ctx;
        
        // 绘制方块背景
        ctx.fillStyle = CONFIG.animals.types[tile.type] ? '#FFFFFF' : '#CCCCCC';
        ctx.fillRect(tile.x, tile.y, CONFIG.board.tileSize, CONFIG.board.tileSize);

        // 绘制边框
        ctx.strokeStyle = tile.selected ? '#FF5722' : '#DDDDDD';
        ctx.lineWidth = tile.selected ? 3 : 1;
        ctx.strokeRect(tile.x, tile.y, CONFIG.board.tileSize, CONFIG.board.tileSize);

        // 绘制萌宠（这里用文字代替图片）
        ctx.fillStyle = '#333333';
        ctx.font = 'bold 40px Arial';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        
        const animalNames = ['🐱', '🐶', '🐘', '🦊', '🐸', '🐵', '🐼', '🐰', '🐯'];
        ctx.fillText(animalNames[tile.type] || '?', 
                    tile.x + CONFIG.board.tileSize/2, 
                    tile.y + CONFIG.board.tileSize/2);

        // 绘制特效标识
        if (tile.effect === 'rocket') {
            ctx.fillStyle = '#FF9800';
            ctx.font = '20px Arial';
            ctx.fillText('🚀', tile.x + 15, tile.y + 20);
        } else if (tile.effect === 'bomb') {
            ctx.fillStyle = '#F44336';
            ctx.font = '20px Arial';
            ctx.fillText('💣', tile.x + 15, tile.y + 20);
        }
    }

    /**
     * 绘制道具栏
     */
    drawProps() {
        const ctx = this.ctx;
        const layout = CONFIG.layout.game;
        const props = ['refresh', 'bomb', 'clear'];
        
        props.forEach((prop, index) => {
            const x = layout.props.x + (index - 1) * 100;
            const y = layout.props.y;
            
            // 绘制道具背景
            ctx.fillStyle = this.gameState.props[prop] > 0 ? '#4CAF50' : '#CCCCCC';
            ctx.fillRect(x - 40, y - 20, 80, 40);
            
            // 绘制道具名称
            ctx.fillStyle = '#FFFFFF';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(CONFIG.props[prop].name, x, y);
            
            // 绘制数量
            ctx.font = '12px Arial';
            ctx.fillText(this.gameState.props[prop].toString(), x, y + 15);
        });
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        tt.onTouchStart(this.handleTouchStart.bind(this));
        tt.onTouchMove(this.handleTouchMove.bind(this));
        tt.onTouchEnd(this.handleTouchEnd.bind(this));
    }

    /**
     * 处理触摸开始
     */
    handleTouchStart(e) {
        if (this.gameState.isAnimating) return;
        
        const touch = e.touches[0];
        const tile = this.getTileAtPosition(touch.clientX, touch.clientY);
        
        if (tile) {
            this.gameState.selectedTile = tile;
            tile.selected = true;
            this.draw();
        }
    }

    /**
     * 处理触摸移动
     */
    handleTouchMove(e) {
        // 可以添加拖拽效果
    }

    /**
     * 处理触摸结束
     */
    handleTouchEnd(e) {
        if (this.gameState.isAnimating || !this.gameState.selectedTile) return;
        
        const touch = e.changedTouches[0];
        const tile = this.getTileAtPosition(touch.clientX, touch.clientY);
        
        if (tile && tile !== this.gameState.selectedTile) {
            // 检查是否相邻
            if (this.isAdjacent(this.gameState.selectedTile, tile)) {
                this.swapTiles(this.gameState.selectedTile, tile);
                this.processMatches();
            }
        }
        
        // 清除选择
        if (this.gameState.selectedTile) {
            this.gameState.selectedTile.selected = false;
            this.gameState.selectedTile = null;
        }
        
        this.draw();
    }

    /**
     * 获取指定位置的方块
     */
    getTileAtPosition(x, y) {
        for (let row = 0; row < CONFIG.board.rows; row++) {
            for (let col = 0; col < CONFIG.board.cols; col++) {
                const tile = this.gameState.board[row][col];
                if (tile &&
                    x >= tile.x && x <= tile.x + CONFIG.board.tileSize &&
                    y >= tile.y && y <= tile.y + CONFIG.board.tileSize) {
                    return tile;
                }
            }
        }
        return null;
    }

    /**
     * 检查是否相邻
     */
    isAdjacent(tile1, tile2) {
        const rowDiff = Math.abs(tile1.row - tile2.row);
        const colDiff = Math.abs(tile1.col - tile2.col);
        return (rowDiff === 1 && colDiff === 0) || (rowDiff === 0 && colDiff === 1);
    }

    /**
     * 交换方块
     */
    swapTiles(tile1, tile2) {
        // 交换类型
        const tempType = tile1.type;
        tile1.type = tile2.type;
        tile2.type = tempType;
        
        // 交换特效
        const tempEffect = tile1.effect;
        tile1.effect = tile2.effect;
        tile2.effect = tempEffect;
    }

    /**
     * 处理消除
     */
    processMatches() {
        const matches = this.findMatches();

        if (matches.length > 0) {
            this.gameState.isAnimating = true;

            // 计算得分
            let matchScore = 0;
            matches.forEach(match => {
                matchScore += match.length * CONFIG.score.baseScore;
            });

            // 连击加成
            this.gameState.combo++;
            const multiplier = CONFIG.score.comboMultiplier[this.gameState.combo] || 2.0;
            matchScore = Math.floor(matchScore * multiplier);

            this.gameState.score += matchScore;

            // 播放音效
            this.playMatchSound();

            // 移除匹配的方块
            this.removeMatches(matches);

            // 生成特效方块
            this.generateSpecialTiles(matches);

            // 下落和填充
            setTimeout(() => {
                this.dropTiles();
                this.fillEmpty();

                // 检查连锁反应
                setTimeout(() => {
                    this.processMatches();
                }, 300);
            }, 300);

            // 检查是否达到目标分数
            this.checkWinCondition();

        } else {
            // 没有匹配，重置连击
            this.gameState.combo = 0;
            this.gameState.isAnimating = false;

            // 检查是否还有可行移动
            if (!this.hasValidMoves()) {
                this.handleNoMoves();
            }
        }
    }

    /**
     * 播放匹配音效
     */
    playMatchSound() {
        if (this.gameState.combo >= 5) {
            this.app.playSound('combo5');
        } else if (this.gameState.combo >= 3) {
            this.app.playSound('combo3');
        } else {
            this.app.playSound('match');
        }
    }

    /**
     * 检查胜利条件
     */
    checkWinCondition() {
        if (this.gameState.score >= this.gameState.targetScore) {
            setTimeout(() => {
                this.endGame(true);
            }, 1000);
        }
    }

    /**
     * 查找匹配
     */
    findMatches() {
        const matches = [];
        
        // 检查横向匹配
        for (let row = 0; row < CONFIG.board.rows; row++) {
            let count = 1;
            let currentType = this.gameState.board[row][0].type;
            
            for (let col = 1; col < CONFIG.board.cols; col++) {
                if (this.gameState.board[row][col].type === currentType) {
                    count++;
                } else {
                    if (count >= 3) {
                        matches.push({
                            type: 'horizontal',
                            row: row,
                            startCol: col - count,
                            length: count,
                            tiles: Array.from({length: count}, (_, i) => this.gameState.board[row][col - count + i])
                        });
                    }
                    count = 1;
                    currentType = this.gameState.board[row][col].type;
                }
            }
            
            if (count >= 3) {
                matches.push({
                    type: 'horizontal',
                    row: row,
                    startCol: CONFIG.board.cols - count,
                    length: count,
                    tiles: Array.from({length: count}, (_, i) => this.gameState.board[row][CONFIG.board.cols - count + i])
                });
            }
        }
        
        // 检查纵向匹配
        for (let col = 0; col < CONFIG.board.cols; col++) {
            let count = 1;
            let currentType = this.gameState.board[0][col].type;
            
            for (let row = 1; row < CONFIG.board.rows; row++) {
                if (this.gameState.board[row][col].type === currentType) {
                    count++;
                } else {
                    if (count >= 3) {
                        matches.push({
                            type: 'vertical',
                            col: col,
                            startRow: row - count,
                            length: count,
                            tiles: Array.from({length: count}, (_, i) => this.gameState.board[row - count + i][col])
                        });
                    }
                    count = 1;
                    currentType = this.gameState.board[row][col].type;
                }
            }
            
            if (count >= 3) {
                matches.push({
                    type: 'vertical',
                    col: col,
                    startRow: CONFIG.board.rows - count,
                    length: count,
                    tiles: Array.from({length: count}, (_, i) => this.gameState.board[CONFIG.board.rows - count + i][col])
                });
            }
        }
        
        return matches;
    }

    /**
     * 移除匹配的方块
     */
    removeMatches(matches) {
        matches.forEach(match => {
            match.tiles.forEach(tile => {
                tile.type = -1; // 标记为已移除
            });
        });
    }

    /**
     * 生成特效方块
     */
    generateSpecialTiles(matches) {
        matches.forEach(match => {
            if (match.length === 4) {
                // 生成火箭
                const centerTile = match.tiles[Math.floor(match.length / 2)];
                centerTile.type = Math.floor(Math.random() * CONFIG.levels[this.gameState.level - 1].animalTypes);
                centerTile.effect = 'rocket';
            } else if (match.length >= 5) {
                // 生成炸弹
                const centerTile = match.tiles[Math.floor(match.length / 2)];
                centerTile.type = Math.floor(Math.random() * CONFIG.levels[this.gameState.level - 1].animalTypes);
                centerTile.effect = 'bomb';
            }
        });
    }

    /**
     * 方块下落
     */
    dropTiles() {
        for (let col = 0; col < CONFIG.board.cols; col++) {
            let writeRow = CONFIG.board.rows - 1;
            
            for (let row = CONFIG.board.rows - 1; row >= 0; row--) {
                if (this.gameState.board[row][col].type !== -1) {
                    if (row !== writeRow) {
                        this.gameState.board[writeRow][col] = { ...this.gameState.board[row][col] };
                        this.gameState.board[writeRow][col].row = writeRow;
                        this.gameState.board[writeRow][col].y = CONFIG.board.startY + writeRow * (CONFIG.board.tileSize + CONFIG.board.padding);
                    }
                    writeRow--;
                }
            }
            
            // 清空上方空位
            for (let row = writeRow; row >= 0; row--) {
                this.gameState.board[row][col] = {
                    type: -1,
                    x: CONFIG.board.startX + col * (CONFIG.board.tileSize + CONFIG.board.padding),
                    y: CONFIG.board.startY + row * (CONFIG.board.tileSize + CONFIG.board.padding),
                    row: row,
                    col: col,
                    selected: false,
                    effect: null
                };
            }
        }
    }

    /**
     * 填充空白位置
     */
    fillEmpty() {
        const levelConfig = CONFIG.levels[this.gameState.level - 1];
        
        for (let row = 0; row < CONFIG.board.rows; row++) {
            for (let col = 0; col < CONFIG.board.cols; col++) {
                if (this.gameState.board[row][col].type === -1) {
                    this.gameState.board[row][col].type = Math.floor(Math.random() * levelConfig.animalTypes);
                }
            }
        }
    }

    /**
     * 处理无可用移动的情况
     */
    handleNoMoves() {
        if (this.gameState.props.refresh > 0) {
            this.gameState.props.refresh--;
            this.shuffleBoard();
            this.app.playSound('match');
        } else {
            // 游戏结束
            this.endGame(false);
        }
    }

    /**
     * 打乱棋盘
     */
    shuffleBoard() {
        const animals = [];
        for (let row = 0; row < CONFIG.board.rows; row++) {
            for (let col = 0; col < CONFIG.board.cols; col++) {
                if (this.gameState.board[row][col].type !== -1) {
                    animals.push(this.gameState.board[row][col].type);
                }
            }
        }

        // Fisher-Yates洗牌算法
        for (let i = animals.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [animals[i], animals[j]] = [animals[j], animals[i]];
        }

        // 重新填充棋盘
        let index = 0;
        for (let row = 0; row < CONFIG.board.rows; row++) {
            for (let col = 0; col < CONFIG.board.cols; col++) {
                if (this.gameState.board[row][col].type !== -1) {
                    this.gameState.board[row][col].type = animals[index++];
                }
            }
        }

        // 确保有可行移动
        if (!this.hasValidMoves()) {
            this.shuffleBoard();
        }
    }

    /**
     * 结束游戏
     */
    endGame(isWin) {
        this.gameState.isAnimating = false;
        
        if (isWin) {
            this.app.playSound('win');
            // 通关逻辑
            if (this.gameState.level < CONFIG.levels.length) {
                this.app.globalData.currentLevel = this.gameState.level + 1;
            }
        } else {
            this.app.playSound('lose');
        }

        // 更新最高分
        if (this.gameState.score > this.app.globalData.highScore) {
            this.app.globalData.highScore = this.gameState.score;
        }

        // 保存数据
        this.app.saveGlobalData();

        // 显示结果
        setTimeout(() => {
            this.showResult(isWin);
        }, 1000);
    }

    /**
     * 显示游戏结果
     */
    showResult(isWin) {
        const ctx = this.ctx;
        
        // 半透明背景
        ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
        ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        
        // 结果文字
        ctx.fillStyle = '#FFFFFF';
        ctx.font = 'bold 48px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(isWin ? '恭喜通关！' : '游戏结束', this.canvas.width / 2, this.canvas.height / 2 - 50);
        
        ctx.font = 'bold 36px Arial';
        ctx.fillText(`最终分数: ${this.gameState.score}`, this.canvas.width / 2, this.canvas.height / 2 + 20);
        
        // 按钮
        this.drawButton('重新开始', this.canvas.width / 2 - 100, this.canvas.height / 2 + 100, 80, 40, '#4CAF50');
        this.drawButton('返回主页', this.canvas.width / 2 + 100, this.canvas.height / 2 + 100, 80, 40, '#FF9800');
        
        // 绑定结果事件
        tt.offTouchStart();
        tt.onTouchStart((e) => {
            const touch = e.touches[0];
            const x = touch.clientX;
            const y = touch.clientY;
            
            if (this.isPointInRect(x, y, this.canvas.width / 2 - 100, this.canvas.height / 2 + 100, 80, 40)) {
                // 重新开始
                this.init();
            } else if (this.isPointInRect(x, y, this.canvas.width / 2 + 100, this.canvas.height / 2 + 100, 80, 40)) {
                // 返回主页
                this.app.showHomePage();
            }
        });
    }

    /**
     * 绘制按钮
     */
    drawButton(text, x, y, width, height, color) {
        const ctx = this.ctx;
        
        ctx.fillStyle = color;
        ctx.fillRect(x, y, width, height);
        
        ctx.fillStyle = '#FFFFFF';
        ctx.font = '14px Arial';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(text, x + width/2, y + height/2);
    }

    /**
     * 检查点是否在矩形内
     */
    isPointInRect(x, y, rectX, rectY, width, height) {
        return x >= rectX && x <= rectX + width && y >= rectY && y <= rectY + height;
    }

    /**
     * 返回主页
     */
    back() {
        tt.offTouchStart();
        tt.offTouchMove();
        tt.offTouchEnd();
        this.app.showHomePage();
    }
}

module.exports = GamePage;