/**
 * 萌宠爱消消 - 设置页面
 * 游戏设置和偏好配置
 */

const CONFIG = require('../config.js');

class SettingPage {
    constructor(app) {
        this.app = app;
        this.canvas = app.canvas;
        this.ctx = app.ctx;

        // 设置状态
        this.settings = {
            muteAll: false,
            musicVolume: 80,
            soundVolume: 80,
            theme: 'default'
        };

        // 滑块拖拽状态
        this.dragging = {
            music: false,
            sound: false
        };

        this.init();
    }

    /**
     * 初始化设置页面
     */
    init() {
        this.loadSettings();
        this.draw();
        this.bindEvents();
    }

    /**
     * 加载设置
     */
    loadSettings() {
        const audioSettings = this.app.audioManager.getSettings();
        this.settings.muteAll = audioSettings.muteAll;
        this.settings.musicVolume = Math.round(audioSettings.musicVolume * 100);
        this.settings.soundVolume = Math.round(audioSettings.soundVolume * 100);
        this.settings.theme = this.app.globalData.theme;
    }

    /**
     * 绘制设置页面
     */
    draw() {
        this.clearCanvas();
        this.drawBackground();
        this.drawHeader();
        this.drawMuteSwitch();
        this.drawVolumeControls();
        this.drawThemeSelector();
    }

    /**
     * 清空画布
     */
    clearCanvas() {
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
    }

    /**
     * 绘制背景
     */
    drawBackground() {
        const ctx = this.ctx;

        // 渐变背景
        const gradient = ctx.createLinearGradient(0, 0, 0, this.canvas.height);
        gradient.addColorStop(0, '#F9F0FA');
        gradient.addColorStop(1, '#F0E5F5');

        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
    }

    /**
     * 绘制页面头部
     */
    drawHeader() {
        const ctx = this.ctx;

        // 返回按钮
        ctx.fillStyle = CONFIG.ui.colors.text;
        ctx.font = `${CONFIG.ui.fonts.sizes.medium}px ${CONFIG.ui.fonts.primary}`;
        ctx.textAlign = 'left';
        ctx.fillText('← 返回', 16, 50);

        // 页面标题
        ctx.fillStyle = CONFIG.ui.colors.text;
        ctx.font = `bold ${CONFIG.ui.fonts.sizes.large}px ${CONFIG.ui.fonts.primary}`;
        ctx.textAlign = 'center';
        ctx.fillText('设置', this.canvas.width / 2, 50);
    }

    /**
     * 绘制静音开关
     */
    drawMuteSwitch() {
        const ctx = this.ctx;
        const startY = 100;
        const cardHeight = 80;

        // 背景卡片
        ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
        ctx.beginPath();
        ctx.roundRect(16, startY, this.canvas.width - 32, cardHeight, 8);
        ctx.fill();

        // 静音图标和文字
        ctx.fillStyle = CONFIG.ui.colors.text;
        ctx.font = `${CONFIG.ui.fonts.sizes.xlarge}px ${CONFIG.ui.fonts.primary}`;
        ctx.textAlign = 'left';
        ctx.fillText('🔇', 30, startY + cardHeight/2 + 8);

        ctx.font = `${CONFIG.ui.fonts.sizes.medium}px ${CONFIG.ui.fonts.primary}`;
        ctx.fillText('静音模式', 70, startY + cardHeight/2 + 6);

        // 开关
        this.drawSwitch(this.canvas.width - 80, startY + cardHeight/2, this.settings.muteAll);
    }

    /**
     * 绘制音量控制
     */
    drawVolumeControls() {
        const ctx = this.ctx;
        let currentY = 200;

        // 背景音量控制
        this.drawVolumeControl(currentY, '🎵', '背景音量', this.settings.musicVolume, 'music');
        currentY += 100;

        // 音效音量控制
        this.drawVolumeControl(currentY, '🔊', '音效音量', this.settings.soundVolume, 'sound');
    }

    /**
     * 绘制单个音量控制
     */
    drawVolumeControl(y, icon, label, volume, type) {
        const ctx = this.ctx;
        const cardHeight = 80;
        const isDisabled = this.settings.muteAll;

        // 背景卡片
        ctx.fillStyle = isDisabled ? 'rgba(255, 255, 255, 0.4)' : 'rgba(255, 255, 255, 0.8)';
        ctx.beginPath();
        ctx.roundRect(16, y, this.canvas.width - 32, cardHeight, 8);
        ctx.fill();

        // 图标和文字
        ctx.fillStyle = isDisabled ? CONFIG.ui.colors.textLight : CONFIG.ui.colors.text;
        ctx.font = `${CONFIG.ui.fonts.sizes.xlarge}px ${CONFIG.ui.fonts.primary}`;
        ctx.textAlign = 'left';
        ctx.fillText(icon, 30, y + cardHeight/2 + 8);

        ctx.font = `${CONFIG.ui.fonts.sizes.medium}px ${CONFIG.ui.fonts.primary}`;
        ctx.fillText(label, 70, y + cardHeight/2 + 6);

        // 滑块
        this.drawSlider(200, y + cardHeight/2, 200, volume, isDisabled);
    }

    /**
     * 绘制主题选择器
     */
    drawThemeSelector() {
        const ctx = this.ctx;
        const startY = 420;
        const cardHeight = 80;

        // 背景卡片
        ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
        ctx.beginPath();
        ctx.roundRect(16, startY, this.canvas.width - 32, cardHeight, 8);
        ctx.fill();

        // 主题图标和文字
        ctx.fillStyle = CONFIG.ui.colors.text;
        ctx.font = `${CONFIG.ui.fonts.sizes.xlarge}px ${CONFIG.ui.fonts.primary}`;
        ctx.textAlign = 'left';
        ctx.fillText('🎨', 30, startY + cardHeight/2 + 8);

        ctx.font = `${CONFIG.ui.fonts.sizes.medium}px ${CONFIG.ui.fonts.primary}`;
        ctx.fillText('主题选择', 70, startY + cardHeight/2 + 6);

        // 当前主题显示
        ctx.fillStyle = CONFIG.ui.colors.primary;
        ctx.font = `${CONFIG.ui.fonts.sizes.medium}px ${CONFIG.ui.fonts.primary}`;
        ctx.textAlign = 'right';
        ctx.fillText('通用主题', this.canvas.width - 30, startY + cardHeight/2 + 6);
    }