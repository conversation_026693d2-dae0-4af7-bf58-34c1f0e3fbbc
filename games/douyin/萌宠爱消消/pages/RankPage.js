/**
 * 萌宠爱消消 - 排行榜页面
 * 显示玩家排名和分数信息
 */

const CONFIG = require('../config.js');

class RankPage {
    constructor(app) {
        this.app = app;
        this.canvas = app.canvas;
        this.ctx = app.ctx;

        // 页面状态
        this.currentTab = 'all'; // 'all', 'region', 'friends'
        this.rankData = {
            all: [],
            region: [],
            friends: []
        };
        this.userRank = {
            rank: 12,
            score: 8500
        };

        this.init();
    }

    /**
     * 初始化排行榜页面
     */
    init() {
        this.loadRankData();
        this.draw();
        this.bindEvents();
    }

    /**
     * 加载排行榜数据
     */
    loadRankData() {
        // 模拟排行榜数据
        this.rankData.all = [
            { rank: 1, name: '萌宠大师', score: 15000, avatar: '🐱' },
            { rank: 2, name: '消除达人', score: 12500, avatar: '🐶' },
            { rank: 3, name: '连击王者', score: 11000, avatar: '🐼' },
            { rank: 4, name: '萌宠新星', score: 9800, avatar: '🐰' },
            { rank: 5, name: '消消乐手', score: 9200, avatar: '🦊' },
            { rank: 6, name: '萌宠爱好者', score: 8800, avatar: '🐸' },
            { rank: 7, name: '游戏高手', score: 8600, avatar: '🐵' },
            { rank: 8, name: '萌宠收集家', score: 8400, avatar: '🐘' },
            { rank: 9, name: '消除专家', score: 8200, avatar: '🐯' },
            { rank: 10, name: '萌宠守护者', score: 8000, avatar: '🐱' }
        ];

        // 复制数据到其他标签
        this.rankData.region = [...this.rankData.all];
        this.rankData.friends = this.rankData.all.slice(0, 5);
    }

    /**
     * 绘制排行榜页面
     */
    draw() {
        this.clearCanvas();
        this.drawBackground();
        this.drawHeader();
        this.drawUserRank();
        this.drawTabs();
        this.drawRankList();
        this.drawFooter();
    }

    /**
     * 清空画布
     */
    clearCanvas() {
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
    }

    /**
     * 绘制背景
     */
    drawBackground() {
        const ctx = this.ctx;

        // 渐变背景
        const gradient = ctx.createLinearGradient(0, 0, 0, this.canvas.height);
        gradient.addColorStop(0, '#FFF5F8');
        gradient.addColorStop(1, '#FFEFF3');

        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
    }

    /**
     * 绘制页面头部
     */
    drawHeader() {
        const ctx = this.ctx;

        // 返回按钮
        ctx.fillStyle = CONFIG.ui.colors.text;
        ctx.font = `${CONFIG.ui.fonts.sizes.medium}px ${CONFIG.ui.fonts.primary}`;
        ctx.textAlign = 'left';
        ctx.fillText('← 返回', 16, 50);

        // 页面标题
        ctx.fillStyle = CONFIG.ui.colors.text;
        ctx.font = `bold ${CONFIG.ui.fonts.sizes.large}px ${CONFIG.ui.fonts.primary}`;
        ctx.textAlign = 'center';
        ctx.fillText('排行榜', this.canvas.width / 2, 50);
    }

    /**
     * 绘制用户排名区域
     */
    drawUserRank() {
        const ctx = this.ctx;
        const startY = 80;
        const height = 120;

        // 背景卡片
        ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
        ctx.beginPath();
        ctx.roundRect(16, startY, this.canvas.width - 32, height, 12);
        ctx.fill();

        // 用户头像
        ctx.fillStyle = CONFIG.ui.colors.primary;
        ctx.beginPath();
        ctx.arc(60, startY + height/2, 30, 0, 2 * Math.PI);
        ctx.fill();

        ctx.fillStyle = CONFIG.ui.colors.white;
        ctx.font = `${CONFIG.ui.fonts.sizes.xlarge}px ${CONFIG.ui.fonts.primary}`;
        ctx.textAlign = 'center';
        ctx.fillText('🐱', 60, startY + height/2 + 8);

        // 排名信息
        ctx.fillStyle = CONFIG.ui.colors.textLight;
        ctx.font = `${CONFIG.ui.fonts.sizes.medium}px ${CONFIG.ui.fonts.primary}`;
        ctx.textAlign = 'left';
        ctx.fillText(`我的排名：${this.userRank.rank}`, 110, startY + height/2 - 10);

        // 得分信息
        ctx.fillStyle = CONFIG.ui.colors.primary;
        ctx.font = `bold ${CONFIG.ui.fonts.sizes.large}px ${CONFIG.ui.fonts.primary}`;
        ctx.fillText(`我的得分：${this.userRank.score}`, 110, startY + height/2 + 20);
    }

    /**
     * 绘制标签切换区
     */
    drawTabs() {
        const ctx = this.ctx;
        const startY = 220;
        const tabWidth = this.canvas.width / 3;
        const tabs = [
            { key: 'all', text: '全部' },
            { key: 'region', text: '地区' },
            { key: 'friends', text: '好友' }
        ];

        tabs.forEach((tab, index) => {
            const x = index * tabWidth;
            const isSelected = tab.key === this.currentTab;

            // 标签文字
            ctx.fillStyle = isSelected ? CONFIG.ui.colors.primary : CONFIG.ui.colors.textLight;
            ctx.font = `${CONFIG.ui.fonts.sizes.medium}px ${CONFIG.ui.fonts.primary}`;
            ctx.textAlign = 'center';
            ctx.fillText(tab.text, x + tabWidth/2, startY + 20);

            // 选中指示器
            if (isSelected) {
                ctx.fillStyle = CONFIG.ui.colors.primary;
                const indicatorWidth = tabWidth * 0.8;
                ctx.fillRect(x + tabWidth * 0.1, startY + 30, indicatorWidth, 2);
            }
        });
    }