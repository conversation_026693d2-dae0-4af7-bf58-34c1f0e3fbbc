/**
 * 萌宠爱消消 - 音频管理器
 * 负责游戏音效和背景音乐的播放管理
 */

const CONFIG = require('../config.js');

class AudioManager {
    constructor() {
        this.backgroundMusic = null;
        this.soundEffects = {};
        this.settings = {
            musicEnabled: true,
            soundEnabled: true,
            musicVolume: 0.8,
            soundVolume: 0.8,
            muteAll: false
        };
        
        this.init();
    }
    
    /**
     * 初始化音频管理器
     */
    init() {
        this.loadSettings();
        this.preloadAudio();
    }
    
    /**
     * 加载音频设置
     */
    loadSettings() {
        try {
            const savedSettings = tt.getStorageSync('audio_settings');
            if (savedSettings) {
                this.settings = { ...this.settings, ...savedSettings };
            }
        } catch (error) {
            console.error('加载音频设置失败:', error);
        }
    }
    
    /**
     * 保存音频设置
     */
    saveSettings() {
        try {
            tt.setStorageSync('audio_settings', this.settings);
        } catch (error) {
            console.error('保存音频设置失败:', error);
        }
    }
    
    /**
     * 预加载音频文件
     */
    preloadAudio() {
        try {
            // 预加载背景音乐
            this.backgroundMusic = tt.createInnerAudioContext();
            this.backgroundMusic.src = CONFIG.audio.background;
            this.backgroundMusic.loop = true;
            this.backgroundMusic.volume = this.settings.musicVolume;
            
            // 预加载音效
            Object.keys(CONFIG.audio.effects).forEach(key => {
                const audio = tt.createInnerAudioContext();
                audio.src = CONFIG.audio.effects[key];
                audio.volume = this.settings.soundVolume;
                this.soundEffects[key] = audio;
            });
            
            console.log('音频文件预加载完成');
        } catch (error) {
            console.error('音频预加载失败:', error);
        }
    }
    
    /**
     * 播放背景音乐
     */
    playBackgroundMusic() {
        if (!this.settings.musicEnabled || this.settings.muteAll || !this.backgroundMusic) {
            return;
        }
        
        try {
            this.backgroundMusic.play();
        } catch (error) {
            console.error('播放背景音乐失败:', error);
        }
    }
    
    /**
     * 停止背景音乐
     */
    stopBackgroundMusic() {
        if (this.backgroundMusic) {
            try {
                this.backgroundMusic.stop();
            } catch (error) {
                console.error('停止背景音乐失败:', error);
            }
        }
    }
    
    /**
     * 暂停背景音乐
     */
    pauseBackgroundMusic() {
        if (this.backgroundMusic) {
            try {
                this.backgroundMusic.pause();
            } catch (error) {
                console.error('暂停背景音乐失败:', error);
            }
        }
    }
    
    /**
     * 播放音效
     * @param {string} effectName - 音效名称
     */
    playSound(effectName) {
        if (!this.settings.soundEnabled || this.settings.muteAll) {
            return;
        }
        
        const sound = this.soundEffects[effectName];
        if (sound) {
            try {
                sound.currentTime = 0; // 重置播放位置
                sound.play();
            } catch (error) {
                console.error(`播放音效 ${effectName} 失败:`, error);
            }
        } else {
            console.warn(`音效 ${effectName} 不存在`);
        }
    }
    
    /**
     * 设置音乐音量
     * @param {number} volume - 音量 (0-1)
     */
    setMusicVolume(volume) {
        this.settings.musicVolume = Math.max(0, Math.min(1, volume));
        if (this.backgroundMusic) {
            this.backgroundMusic.volume = this.settings.musicVolume;
        }
        this.saveSettings();
    }
    
    /**
     * 设置音效音量
     * @param {number} volume - 音量 (0-1)
     */
    setSoundVolume(volume) {
        this.settings.soundVolume = Math.max(0, Math.min(1, volume));
        Object.values(this.soundEffects).forEach(sound => {
            sound.volume = this.settings.soundVolume;
        });
        this.saveSettings();
    }
    
    /**
     * 切换音乐开关
     */
    toggleMusic() {
        this.settings.musicEnabled = !this.settings.musicEnabled;
        if (this.settings.musicEnabled) {
            this.playBackgroundMusic();
        } else {
            this.stopBackgroundMusic();
        }
        this.saveSettings();
    }
    
    /**
     * 切换音效开关
     */
    toggleSound() {
        this.settings.soundEnabled = !this.settings.soundEnabled;
        this.saveSettings();
    }
    
    /**
     * 切换静音模式
     */
    toggleMute() {
        this.settings.muteAll = !this.settings.muteAll;
        if (this.settings.muteAll) {
            this.stopBackgroundMusic();
        } else if (this.settings.musicEnabled) {
            this.playBackgroundMusic();
        }
        this.saveSettings();
    }
    
    /**
     * 获取当前设置
     */
    getSettings() {
        return { ...this.settings };
    }
    
    /**
     * 销毁音频管理器
     */
    destroy() {
        if (this.backgroundMusic) {
            this.backgroundMusic.destroy();
        }
        
        Object.values(this.soundEffects).forEach(sound => {
            sound.destroy();
        });
        
        this.soundEffects = {};
    }
}

module.exports = AudioManager;
