# 萌宠爱消消 - 抖音原生小游戏

一个基于抖音原生小游戏平台开发的宠物主题消除类休闲游戏。

## 🎮 游戏特色

- **萌宠主题**：9种可爱萌宠角色（猫咪、小狗、兔子等）
- **经典消除**：3消、4消、5消机制，支持连击奖励
- **特效道具**：小火箭、小炸弹等特殊消除效果
- **道具系统**：刷新卡、炸弹卡、清屏卡三种实用道具
- **关卡挑战**：3个难度递增的关卡设计
- **音效丰富**：背景音乐和多种游戏音效
- **数据存储**：本地数据持久化保存

## 📱 游戏界面

### 主页面
- 萌宠主题标题
- 开始游戏、排行榜、设置三个主要功能按钮
- 版权信息显示

### 游戏页面
- 8×10网格游戏区域
- 实时分数和目标分数显示
- 道具栏（刷新卡、炸弹卡、清屏卡）
- 暂停菜单功能

### 排行榜页面
- 前三名特殊展示（金银铜牌）
- 个人排名和分数显示
- 全部/地区/好友三个标签切换

### 设置页面
- 静音模式总开关
- 背景音量和音效音量独立控制
- 主题选择功能

## 🎯 游戏规则

### 基础消除
- **3消**：获得基础分（20分×倍率）
- **4消**：生成横向/纵向消除的小火箭
- **5消**：生成3×3范围消除的小炸弹

### 连击系统
- ≤3次：1.0x倍率
- 4-5次：1.5x倍率
- >5次：2.0x倍率

### 道具效果
| 道具 | 效果 | 得分 |
|------|------|------|
| 小火箭+普通动物 | 消除整列 | 100分 |
| 小火箭+小火箭 | 十字消除 | 200分 |
| 小炸弹+普通动物 | 3×3范围消除 | 300分 |
| 小炸弹+小火箭 | 3列消除 | 400分 |
| 小炸弹+小炸弹 | 5×5范围爆炸 | 500分 |

### 关卡设计
| 关卡 | 标题 | 目标分数 | 萌宠种类 | 难度 |
|------|------|----------|----------|------|
| 1 | 萌宠新手村 | 1000分 | 5种 | 非常简单 |
| 2 | 萌宠大练兵 | 4000分 | 7种 | 简单 |
| 3 | 萌宠总动员 | 8000分 | 9种 | 超级困难 |

## 🛠️ 技术架构

### 核心技术
- **平台**：抖音原生小游戏
- **语言**：JavaScript ES6+
- **渲染**：Canvas 2D
- **架构**：模块化设计

### 项目结构
```
萌宠爱消消/
├── game.js              # 主游戏入口
├── config.js            # 游戏配置文件
├── game.json            # 小游戏配置
├── manager/             # 管理器模块
│   ├── audioManager.js  # 音频管理
│   ├── dataManager.js   # 数据管理
│   └── themeManager.js  # 主题管理
├── pages/               # 页面模块
│   ├── GamePage.js      # 游戏页面
│   ├── RankPage.js      # 排行榜页面
│   └── SettingPage.js   # 设置页面
├── images/              # 图片资源
│   ├── animal/          # 萌宠图片
│   ├── button/          # 按钮图片
│   ├── icon/            # 图标
│   └── prop/            # 道具图片
└── audios/              # 音频资源
```

### 核心模块

#### 游戏管理器 (Game)
- 页面路由管理
- 全局状态管理
- 事件分发处理

#### 音频管理器 (AudioManager)
- 背景音乐播放控制
- 音效播放管理
- 音量设置保存

#### 数据管理器 (DataManager)
- 游戏数据持久化
- 分数和关卡管理
- 设置信息存储

#### 主题管理器 (ThemeManager)
- UI主题配置
- 颜色方案管理
- 按钮样式统一

## 🎵 音效系统

| 游戏事件 | 音效文件 | 说明 |
|----------|----------|------|
| 普通消除 | audios/so.mp3 | 3消音效 |
| 小火箭消除 | audios/shua.mp3 | 火箭特效音 |
| 小炸弹触发 | audios/bomb.mp3 | 爆炸音效 |
| 3连击 | audios/wa.mp3 | 连击音效 |
| 5连击 | audios/good.mp3 | 高连击音效 |
| 胜利 | audios/win.mp3 | 通关音效 |
| 失败 | audios/lose.mp3 | 游戏结束音效 |
| 背景音乐 | audios/background.mp3 | 循环播放 |

## 🚀 运行说明

### 开发环境
1. 安装抖音开发者工具
2. 导入项目目录
3. 配置小游戏appid
4. 点击预览或真机调试

### 测试验证
```bash
# 进入项目目录
cd "games/douyin/萌宠爱消消"

# 运行测试文件
node test.js
```

### 部署发布
1. 在抖音开发者工具中点击上传
2. 登录抖音开放平台管理后台
3. 提交审核并发布

## 📋 功能清单

### ✅ 已实现功能
- [x] 游戏主页面设计
- [x] 8×10消除游戏逻辑
- [x] 3消、4消、5消机制
- [x] 连击倍率系统
- [x] 道具卡系统（刷新、炸弹、清屏）
- [x] 三关卡设计
- [x] 音效系统
- [x] 排行榜页面
- [x] 设置页面（音量控制、静音）
- [x] 数据持久化存储
- [x] 暂停和重新开始功能
- [x] 复活机制（每局2次免费复活）

### 🔄 可扩展功能
- [ ] 更多萌宠角色
- [ ] 社交分享功能
- [ ] 成就系统
- [ ] 每日任务
- [ ] 商店系统
- [ ] 更多主题皮肤

## 📄 许可证

© 2023 萌宠爱消消 版权所有

---

**开发完成时间**：2023年12月
**技术支持**：基于抖音原生小游戏平台
